package com.lct.finance.model.enums;

/**
 * 统一错误码枚举
 * 按照业务领域分类，每个领域使用不同的错误码范围
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
public enum ErrorCode {

    // 通用错误 (1000-1999)
    SUCCESS(1000, "成功", 200),
    PARAM_ERROR(1001, "参数错误", 400),
    UNAUTHORIZED(1002, "未授权", 401),
    FORBIDDEN(1003, "禁止访问", 403),
    NOT_FOUND(1004, "资源不存在", 404),
    METHOD_NOT_ALLOWED(1005, "方法不允许", 405),
    TOO_MANY_REQUESTS(1006, "请求过于频繁", 429),
    INTERNAL_ERROR(1007, "服务器内部错误", 500),
    SERVICE_UNAVAILABLE(1008, "服务不可用", 503),

    // 用户相关错误 (2000-2999)
    USER_NOT_FOUND(2001, "用户不存在", 404),
    USER_BANNED(2002, "用户已被禁用", 403),
    USER_NOT_VERIFIED(2003, "用户未完成验证", 403),
    USER_STATUS_ABNORMAL(2004, "用户状态异常", 403),
    INVALID_ADDRESS(2005, "钱包地址格式错误", 400),
    INVITE_CODE_REQUIRED(2006, "需要有效的邀请码", 400),
    USER_CREATE_FAILED(2007, "用户创建失败", 500),
    CHAIN_NOT_SUPPORTED(2008, "不支持的区块链网络", 400),

    // 认证相关错误 (3000-3999)
    AUTHENTICATION_FAILED(3001, "认证失败", 401),
    TOKEN_INVALID(3002, "Token无效", 401),
    TOKEN_EXPIRED(3003, "Token已过期", 401),
    SIGNATURE_INVALID(3004, "签名验证失败", 401),
    NONCE_INVALID(3005, "无效的nonce", 401),
    NONCE_EXPIRED(3006, "时间戳过期", 401),
    CAPTCHA_INVALID(3007, "验证码验证失败", 400),
    CAPTCHA_EXPIRED(3008, "验证码已过期", 400),

    // 兑换相关错误 (4000-4999)
    EXCHANGE_INVALID_PARAMETER(4001, "兑换参数验证失败", 400),
    EXCHANGE_INVALID_ADDRESS(4002, "兑换地址格式无效", 400),
    EXCHANGE_INVALID_AMOUNT(4003, "兑换金额无效", 400),
    EXCHANGE_INVALID_COIN_TYPE(4004, "币种类型无效", 400),
    EXCHANGE_INSUFFICIENT_BALANCE(4101, "兑换余额不足", 400),
    EXCHANGE_DISABLED(4102, "兑换功能已关闭", 403),
    EXCHANGE_RATE_EXPIRED(4103, "汇率已过期", 400),
    EXCHANGE_ORDER_NOT_FOUND(4104, "兑换订单不存在", 404),
    EXCHANGE_ORDER_STATUS_ERROR(4105, "兑换订单状态错误", 400),
    EXCHANGE_DUPLICATE_TRANSACTION(4106, "重复交易", 400),
    EXCHANGE_SWFT_SERVICE_ERROR(4301, "SWFT服务异常", 500),
    EXCHANGE_SWFT_ORDER_FAILED(4302, "SWFT订单创建失败", 500),

    // 提现相关错误 (5000-5999)
    WITHDRAWAL_INSUFFICIENT_BALANCE(5001, "提现余额不足", 400),
    WITHDRAWAL_OUTSIDE_TIME_WINDOW(5002, "不在提现时间窗口内", 403),
    WITHDRAWAL_PERCENT_LIMIT(5003, "可提现百分比限制", 403),
    WITHDRAWAL_BANNED(5004, "提现功能已禁用", 403),
    WITHDRAWAL_DAILY_LIMIT_EXCEEDED(5005, "超过每日提现次数限制", 429),
    WITHDRAWAL_ADDRESS_MISMATCH(5006, "接收地址与用户地址不匹配", 400),

    // PHP兼容错误码 - 提现时间窗口
    WITHDRAWAL_TIME_WINDOW_PHP_COMPAT(1013, "提现时间段为:%time%", 403),

    // 礼包相关错误 (6000-6999)
    GIFT_NOT_NEW_USER(6001, "您不是新用户，无法领取礼包", 403),
    GIFT_ALREADY_CLAIMED(6002, "您已领取过新用户礼包", 403),
    GIFT_NOT_ELIGIBLE(6003, "您不符合领取条件", 403),
    GIFT_CLAIM_FAILED(6004, "礼包领取失败", 500),

    // 安全相关错误 (7000-7999)
    SECURITY_HIGH_RISK(7001, "操作风险过高，需要额外验证", 403),
    SECURITY_UNAUTHORIZED_OPERATION(7002, "未授权的操作", 403),
    SECURITY_ERROR(7003, "安全验证错误", 403),

    // 销毁相关错误 (9000-9999)
    BURN_TXHASH_DUPLICATE(1001, "交易哈希重复", 400),
    BURN_TXHASH_FORMAT_ERROR(9001, "交易哈希格式错误", 400),
    BURN_AMOUNT_TOO_SMALL(9002, "销毁数量低于最低要求", 400),
    BURN_USER_STATUS_ERROR(9003, "用户状态异常，无法销毁", 403),
    BURN_EXCHANGE_BALANCE_INSUFFICIENT(1027, "兑换余额不足", 400),
    BURN_CONCURRENT_LIMIT(9004, "销毁操作过于频繁，请稍后重试", 429),
    BURN_CREATE_FAILED(9005, "销毁订单创建失败", 500),
    BURN_SYSTEM_BUSY(9006, "系统繁忙，请稍后重试", 503),

    // 限流相关错误 (8000-8999)
    RATE_LIMIT_EXCEEDED(8001, "请求频率超限", 429);

    private final int code;
    private final String message;
    private final int httpStatus;

    ErrorCode(int code, String message, int httpStatus) {
        this.code = code;
        this.message = message;
        this.httpStatus = httpStatus;
    }

    /**
     * 获取错误码
     * 
     * @return 错误码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取错误消息
     * 
     * @return 错误消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取HTTP状态码
     * 
     * @return HTTP状态码
     */
    public int getHttpStatus() {
        return httpStatus;
    }

    /**
     * 根据错误码查找对应的枚举
     * 
     * @param code 错误码
     * @return 错误码枚举，如果不存在则返回INTERNAL_ERROR
     */
    public static ErrorCode getByCode(int code) {
        for (ErrorCode errorCode : ErrorCode.values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return INTERNAL_ERROR;
    }

    /**
     * 根据旧的错误码映射到新的ErrorCode
     * 
     * @param legacyCode 旧的错误码
     * @return 对应的新ErrorCode
     */
    public static ErrorCode mapLegacyCode(int legacyCode) {
        switch (legacyCode) {
            case 200:
                return SUCCESS;
            case 400:
                return PARAM_ERROR;
            case 401:
                return UNAUTHORIZED;
            case 403:
                return FORBIDDEN;
            case 404:
                return NOT_FOUND;
            case 405:
                return METHOD_NOT_ALLOWED;
            case 429:
                return TOO_MANY_REQUESTS;
            case 500:
                return INTERNAL_ERROR;
            case 503:
                return SERVICE_UNAVAILABLE;
            case 1010:
                return CHAIN_NOT_SUPPORTED;
            case 1011:
                return SIGNATURE_INVALID;
            case 1012:
                return INVALID_ADDRESS;
            case 1014:
                return INVITE_CODE_REQUIRED;
            case 1015:
                return USER_CREATE_FAILED;
            case 1019:
                return NONCE_EXPIRED;
            case 1020:
                return NONCE_INVALID;
            case 501:
                return CAPTCHA_INVALID;
            case 502:
                return CAPTCHA_EXPIRED;
            case 1021:
                return GIFT_ALREADY_CLAIMED;
            case 1022:
                return GIFT_NOT_NEW_USER;
            case 1023:
                return GIFT_CLAIM_FAILED;
            case 1400:
                return SECURITY_ERROR;
            case 1403:
                return SECURITY_HIGH_RISK;
            case 1404:
                return SECURITY_UNAUTHORIZED_OPERATION;
            default:
                return INTERNAL_ERROR;
        }
    }

    /**
     * 根据旧的字符串错误码映射到新的ErrorCode
     * 
     * @param legacyErrorCode 旧的字符串错误码
     * @return 对应的新ErrorCode
     */
    public static ErrorCode mapLegacyStringCode(String legacyErrorCode) {
        if (legacyErrorCode == null || legacyErrorCode.isEmpty()) {
            return INTERNAL_ERROR;
        }

        // 提取业务类型和错误码
        String businessType = "";
        String errorNumber = "";

        if (legacyErrorCode.contains("_")) {
            String[] parts = legacyErrorCode.split("_", 2);
            businessType = parts[0].toUpperCase();
            errorNumber = parts[1];
        } else {
            try {
                // 尝试将整个字符串解析为数字
                int code = Integer.parseInt(legacyErrorCode);
                return mapLegacyCode(code);
            } catch (NumberFormatException e) {
                // 如果不是数字，则使用默认错误
                return INTERNAL_ERROR;
            }
        }

        // 根据业务类型和错误码映射
        switch (businessType) {
            case "EXCHANGE":
                return mapExchangeErrorCode(errorNumber);
            case "WITHDRAWAL":
                return mapWithdrawalErrorCode(errorNumber);
            case "GIFT":
                return mapGiftErrorCode(errorNumber);
            case "AUTH":
                return mapAuthErrorCode(errorNumber);
            case "SEC":
                return mapSecurityErrorCode(errorNumber);
            case "COMMON":
                return mapCommonErrorCode(errorNumber);
            default:
                return INTERNAL_ERROR;
        }
    }

    /**
     * 映射兑换业务错误码
     */
    private static ErrorCode mapExchangeErrorCode(String errorNumber) {
        switch (errorNumber) {
            case "001":
                return EXCHANGE_INVALID_PARAMETER;
            case "002":
                return EXCHANGE_INVALID_ADDRESS;
            case "003":
                return EXCHANGE_INVALID_AMOUNT;
            case "004":
                return EXCHANGE_INVALID_COIN_TYPE;
            case "101":
                return EXCHANGE_INSUFFICIENT_BALANCE;
            case "102":
                return EXCHANGE_DISABLED;
            case "103":
                return EXCHANGE_RATE_EXPIRED;
            case "104":
                return EXCHANGE_ORDER_NOT_FOUND;
            case "105":
                return EXCHANGE_ORDER_STATUS_ERROR;
            case "106":
                return EXCHANGE_DUPLICATE_TRANSACTION;
            case "301":
                return EXCHANGE_SWFT_SERVICE_ERROR;
            case "302":
                return EXCHANGE_SWFT_ORDER_FAILED;
            default:
                return INTERNAL_ERROR;
        }
    }

    /**
     * 映射提现业务错误码
     */
    private static ErrorCode mapWithdrawalErrorCode(String errorNumber) {
        switch (errorNumber) {
            case "1005":
                return WITHDRAWAL_INSUFFICIENT_BALANCE;
            case "1013":
                return WITHDRAWAL_OUTSIDE_TIME_WINDOW;
            case "1025":
                return WITHDRAWAL_PERCENT_LIMIT;
            case "1020":
                return WITHDRAWAL_BANNED;
            case "1029":
                return WITHDRAWAL_DAILY_LIMIT_EXCEEDED;
            case "1026":
                return WITHDRAWAL_ADDRESS_MISMATCH;
            case "401":
                return UNAUTHORIZED;
            case "403":
                return PARAM_ERROR;
            case "429":
                return TOO_MANY_REQUESTS;
            case "500":
                return INTERNAL_ERROR;
            default:
                return INTERNAL_ERROR;
        }
    }

    /**
     * 映射礼包业务错误码
     */
    private static ErrorCode mapGiftErrorCode(String errorNumber) {
        switch (errorNumber) {
            case "101":
                return GIFT_NOT_NEW_USER;
            case "102":
                return GIFT_ALREADY_CLAIMED;
            case "103":
                return GIFT_NOT_ELIGIBLE;
            case "104":
                return GIFT_CLAIM_FAILED;
            case "105":
                return SIGNATURE_INVALID;
            default:
                return INTERNAL_ERROR;
        }
    }

    /**
     * 映射认证业务错误码
     */
    private static ErrorCode mapAuthErrorCode(String errorNumber) {
        switch (errorNumber) {
            case "401":
                return UNAUTHORIZED;
            default:
                return AUTHENTICATION_FAILED;
        }
    }

    /**
     * 映射安全业务错误码
     */
    private static ErrorCode mapSecurityErrorCode(String errorNumber) {
        switch (errorNumber) {
            case "101":
                return SIGNATURE_INVALID;
            case "102":
                return NONCE_INVALID;
            case "103":
                return SECURITY_HIGH_RISK;
            case "104":
                return SECURITY_UNAUTHORIZED_OPERATION;
            default:
                return INTERNAL_ERROR;
        }
    }

    /**
     * 映射通用业务错误码
     */
    private static ErrorCode mapCommonErrorCode(String errorNumber) {
        switch (errorNumber) {
            case "101":
                return USER_BANNED;
            case "102":
                return USER_NOT_VERIFIED;
            case "103":
                return USER_STATUS_ABNORMAL;
            case "201":
                return TOO_MANY_REQUESTS;
            case "202":
                return WITHDRAWAL_DAILY_LIMIT_EXCEEDED;
            case "203":
                return WITHDRAWAL_OUTSIDE_TIME_WINDOW;
            case "301":
                return WITHDRAWAL_INSUFFICIENT_BALANCE;
            case "302":
                return WITHDRAWAL_BANNED;
            case "401":
                return PARAM_ERROR;
            case "402":
                return INVALID_ADDRESS;
            case "403":
                return EXCHANGE_INVALID_AMOUNT;
            case "404":
                return WITHDRAWAL_ADDRESS_MISMATCH;
            case "501":
                return AUTHENTICATION_FAILED;
            case "502":
                return TOKEN_INVALID;
            case "503":
                return TOKEN_EXPIRED;
            case "504":
                return SIGNATURE_INVALID;
            default:
                return INTERNAL_ERROR;
        }
    }
}