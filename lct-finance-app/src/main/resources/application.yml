server:
  port: 8800
  servlet:
    context-path: /
    encoding:
      charset: UTF-8
      enabled: true
      force: true

spring:
  application:
    name: lct-finance-backend
  
  profiles:
    active: dev
  main:
    allow-circular-references: true
  
  # MyBatis-Plus配置
  mybatis-plus:
    mapper-locations: classpath:mapper/*.xml
    configuration:
      map-underscore-to-camel-case: true
    global-config:
      db-config:
        logic-delete-field: deleted
        logic-delete-value: 1
        logic-not-delete-value: 0
  
  # 缓存配置
  cache:
    type: redis
    redis:
      time-to-live: 7200000
      key-prefix: "lct:cache:"
      use-key-prefix: true
      cache-null-values: false
  
  # Jackson配置
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

# 日志配置 - 仅包含公共日志配置
logging:
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{traceId:-}] - %msg%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} [%X{traceId:-}] - %msg%n'
  file:
    name: logs/lct-finance.log
    max-size: 100MB
    max-history: 30
  # 请求响应日志配置
  request-response:
    enabled: true
    max-body-size: 1024
    exclude-paths: "/actuator,/favicon.ico"


# JWT配置
jwt:
  secret: lct-finance-jwt-secret-key-2024-very-long-and-secure-key
  expiration: 86400 # 24小时
  refresh-expiration: 604800 # 7天
  issuer: lct-finance
  
# 应用配置
app:
  # 跨域配置
  cors:
    allowed-origins: 
      - "http://localhost:3000"
      - "https://www.lctweb.xyz"
      - "https://www.lctweb.info"
      - "https://www.lctweb.me"
      - "https://www.lctweb.club"
    allowed-methods: 
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    allowed-headers: "*"
    allow-credentials: true
    max-age: 3600
  
  # Nonce防重放攻击配置
  nonce:
    enabled: true
    key-prefix: "lct:nonce:"
    default-expire: 1200  # 20分钟
    default-time-window: 600  # 10分钟
    max-per-user: 100
    strict-mode: true
    cleanup-interval: 3600  # 1小时
    enable-statistics: true
    statistics-retention: 86400  # 24小时
  
  # 安全配置
  security:
    ignore-urls:
      - "/actuator/**"
      - "/api/index/**"
      - "/api/captcha/**"
      - "/api/user/login"
      - "/error"
  
  # 文件上传配置
  file:
    upload-path: "/uploads"
    max-file-size: 10MB
    allowed-extensions: 
      - jpg
      - jpeg
      - png
      - gif
      - webp
  
  # 系统配置
  system:
    default-language: zh
    supported-languages:
      - zh
      - en
    pagination:
      default-page-size: 20
      max-page-size: 100
