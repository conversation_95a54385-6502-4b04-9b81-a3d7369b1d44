package com.lct.finance.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtEncoder;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationConverter;
import org.springframework.security.oauth2.server.resource.authentication.JwtGrantedAuthoritiesConverter;
import org.springframework.security.oauth2.jose.jws.MacAlgorithm;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfigurationSource;
import io.jsonwebtoken.security.Keys;
import com.nimbusds.jose.jwk.source.ImmutableSecret;
import com.nimbusds.jose.jwk.OctetSequenceKey;
import com.nimbusds.jose.JWSAlgorithm;

import javax.crypto.SecretKey;

/**
 * Spring Security JWT Resource Server 配置
 * 使用标准的OAuth2 Resource Server方式处理JWT
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Configuration
@EnableWebSecurity
public class JwtSecurityConfig {

    @Autowired
    private CorsConfigurationSource corsConfigurationSource;
    @Autowired
    private CustomAuthenticationEntryPoint customAuthenticationEntryPoint;

    @Value("${jwt.secret:lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits}")
    private String jwtSecret;

    /**
     * 安全过滤器链配置
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
            // 禁用CSRF（无状态API不需要）
            .csrf(csrf -> csrf.disable())
            
            // CORS配置
            .cors(cors -> cors.configurationSource(corsConfigurationSource))
            
            // 会话管理（无状态）
            .sessionManagement(session -> 
                session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
            
            // 请求授权配置
            .authorizeRequests(authz -> authz
                // 公开访问的接口
                .antMatchers(
                    "/api/user/login",
                    "/api/user/is_user", 
                    "/api/index_data",
                    "/api/notices",
                    "/api/exchange/basic",
                    "/api/nonce/generate",
                    "/favicon.ico",
                    "/error"
                ).permitAll()
                // 其他请求需要认证
                .anyRequest().authenticated()
            )
            
            // JWT Resource Server配置
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(jwt -> jwt
                    .decoder(jwtDecoder())
                    .jwtAuthenticationConverter(jwtAuthenticationConverter())
                )
                .authenticationEntryPoint(customAuthenticationEntryPoint)
            );

        log.info("JWT Security配置初始化完成");
        return http.build();
    }

    /**
     * JWT 解码器
     * 使用HMAC SHA-512算法验证JWT签名
     */
    @Bean
    public JwtDecoder jwtDecoder() {
        // 确保密钥长度满足HS512要求（至少512位）
        String actualSecret = jwtSecret;
        if (actualSecret.getBytes().length < 64) {
            actualSecret = "lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits-long-to-meet-hs512-requirements";
            log.warn("JWT密钥长度不足，使用默认安全密钥");
        }

        // 使用与JwtEncoder相同的密钥生成方式
        SecretKey secretKey = Keys.hmacShaKeyFor(actualSecret.getBytes());

        return NimbusJwtDecoder.withSecretKey(secretKey)
                .macAlgorithm(MacAlgorithm.HS512)  // 指定使用HS512算法
                .build();
    }

    /**
     * JWT 编码器
     * 用于生成JWT Token
     */
    @Bean
    public JwtEncoder jwtEncoder() {
        // 确保密钥长度满足HS512要求（至少512位）
        String actualSecret = jwtSecret;
        if (actualSecret.getBytes().length < 64) {
            // 使用与JwtDecoder相同的默认密钥
            actualSecret = "lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits-long-to-meet-hs512-requirements";
            log.warn("JWT密钥长度不足，使用默认安全密钥");
        }

        // 创建OctetSequenceKey用于HMAC算法
        OctetSequenceKey key = new OctetSequenceKey.Builder(actualSecret.getBytes())
                .algorithm(JWSAlgorithm.HS512)
                .build();

        return new NimbusJwtEncoder(new ImmutableSecret<>(key.toByteArray()));
    }

    /**
     * JWT 认证转换器
     * 将JWT claims转换为Spring Security的认证对象
     */
    @Bean
    public JwtAuthenticationConverter jwtAuthenticationConverter() {
        JwtGrantedAuthoritiesConverter authoritiesConverter = new JwtGrantedAuthoritiesConverter();
        // 不使用默认的权限前缀
        authoritiesConverter.setAuthorityPrefix("");

        JwtAuthenticationConverter converter = new JwtAuthenticationConverter();
        converter.setJwtGrantedAuthoritiesConverter(authoritiesConverter);

        // 使用自定义的principal提取器
        converter.setPrincipalClaimName("sub"); // JWT的subject字段作为用户标识

        return converter;
    }
}
