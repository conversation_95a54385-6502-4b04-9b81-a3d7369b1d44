package com.lct.finance.utils;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Date;

/**
 * JWT工具类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Component
public class JwtUtils {

    @Value("${jwt.secret:lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits}")
    private String secret;

    @Value("${jwt.expiration:86400}") // 24小时
    private Long expiration;

    @Value("${jwt.refresh-expiration:604800}") // 7天
    private Long refreshExpiration;

    /**
     * 生成JWT Token
     * 符合Spring Security JWT标准格式
     * 
     * @param userAddress 用户地址
     * @return JWT Token
     */
    public String generateToken(String userAddress) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setSubject(userAddress)  // subject字段用作用户标识
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setIssuer("lct-finance")  // 签发者
                .setAudience("lct-finance-client")  // 受众
                .claim("scope", "user")  // 权限范围
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成包含用户ID的JWT Token
     * 符合Spring Security JWT标准格式
     * 
     * @param userAddress 用户地址
     * @param userId 用户ID
     * @return JWT Token
     */
    public String generateTokenWithUserId(String userAddress, Long userId) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setSubject(userAddress)  // subject字段用作用户标识
                .claim("userId", userId)
                .claim("userAddress", userAddress)  // 明确包含用户地址
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setIssuer("lct-finance")  // 签发者
                .setAudience("lct-finance-client")  // 受众
                .claim("scope", "user")  // 权限范围
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 生成刷新Token
     * 
     * @param userAddress 用户地址
     * @return 刷新Token
     */
    public String generateRefreshToken(String userAddress) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshExpiration * 1000);

        return Jwts.builder()
                .setSubject(userAddress)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .setIssuer("lct-finance")
                .setAudience("lct-finance-client")
                .claim("scope", "refresh")  // 刷新token标识
                .claim("tokenType", "refresh")
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从Token中获取用户地址
     * 
     * @param token JWT Token
     * @return 用户地址
     */
    public String getUserAddressFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.getSubject() : null;
    }

    /**
     * 从Token中获取用户ID
     * 
     * @param token JWT Token
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        if (claims != null) {
            Object userIdObj = claims.get("userId");
            if (userIdObj != null) {
                try {
                    return Long.valueOf(userIdObj.toString());
                } catch (NumberFormatException e) {
                    log.warn("无法解析用户ID: {}", userIdObj);
                }
            }
        }
        return null;
    }

    /**
     * 验证Token是否有效
     * 
     * @param token JWT Token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            getClaimsFromToken(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("Invalid JWT token: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查Token是否过期
     * 
     * @param token JWT Token
     * @return 是否过期
     */
    public boolean isTokenExpired(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            return claims != null && claims.getExpiration().before(new Date());
        } catch (Exception e) {
            return true;
        }
    }

    /**
     * 获取Token过期时间
     * 
     * @return 过期时间戳
     */
    public Long getExpirationTime() {
        return System.currentTimeMillis() / 1000 + expiration;
    }

    /**
     * 获取Token刷新时间
     * 
     * @return 刷新时间戳
     */
    public Long getRefreshTime() {
        return System.currentTimeMillis() / 1000 + refreshExpiration;
    }

    /**
     * 检查Token是否需要刷新
     * 
     * @param token JWT Token
     * @return 是否需要刷新
     */
    public boolean shouldRefreshToken(String token) {
        try {
            Claims claims = getClaimsFromToken(token);
            if (claims == null) {
                return false;
            }
            
            Date expiration = claims.getExpiration();
            Date now = new Date();
            long timeUntilExpiration = expiration.getTime() - now.getTime();
            
            // 如果剩余时间小于刷新时间的一半，则需要刷新
            return timeUntilExpiration < (refreshExpiration * 1000 / 2);
        } catch (Exception e) {
            log.error("检查Token刷新状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 从Token中解析Claims
     */
    private Claims getClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (JwtException | IllegalArgumentException e) {
            log.warn("Failed to parse JWT token: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取Token的所有声明（公开方法）
     * 
     * @param token JWT Token
     * @return Claims对象
     */
    public Claims getClaimsFromTokenPublic(String token) {
        return getClaimsFromToken(token);
    }

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        // 确保密钥长度至少512位（64字节）
        String actualSecret = secret;
        if (actualSecret.getBytes().length < 64) {
            // 如果密钥太短，则使用安全的默认密钥
            actualSecret = "lct-finance-secret-key-2024-very-long-secret-for-security-must-be-at-least-512-bits-long-to-meet-hs512-requirements";
            log.warn("配置的JWT密钥太短，使用默认安全密钥");
        }
        return Keys.hmacShaKeyFor(actualSecret.getBytes());
    }
}