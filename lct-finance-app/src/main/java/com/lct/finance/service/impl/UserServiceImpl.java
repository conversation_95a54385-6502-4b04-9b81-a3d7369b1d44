package com.lct.finance.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.lct.finance.config.BlockchainNetworkConfig;
import com.lct.finance.config.SpecialUserConfig;
import com.lct.finance.exception.BusinessException;
import com.lct.finance.mapper.UserMapper;
import com.lct.finance.model.dto.*;
import com.lct.finance.model.entity.Invite;
import com.lct.finance.model.entity.User;
import com.lct.finance.model.entity.Wallet;

import com.lct.finance.service.*;
import com.lct.finance.service.handler.LoginOperationHandler;
import org.springframework.security.oauth2.jwt.JwtClaimsSet;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.JwtEncoderParameters;

import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务实现类
 * 
 * <AUTHOR> Finance Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {

    @Autowired
    private IInviteService inviteService;
    @Autowired
    private IWalletService walletService;
    @Autowired
    private IMintProfitService mintProfitService;
    @Autowired
    private IMintTeamProfitService mintTeamProfitService;
    @Autowired
    private IBurnService burnService;
    @Autowired
    private INonceService nonceService;
    @Autowired
    private JwtEncoder jwtEncoder;
    @Autowired
    private BlockchainNetworkConfig blockchainNetworkConfig;

    @Value("${jwt.expiration:86400}") // 24小时
    private Long jwtExpiration;
    @Autowired
    private ISignatureService signatureService;
    @Autowired
    private IOperationValidationService operationValidationService;

    @Autowired
    private LoginOperationHandler loginOperationHandler;

    @Autowired
    private IConfigService configService;
    @Autowired
    private SpecialUserConfig specialUserConfig;
    @Autowired
    private IOldUserExperienceService oldUserExperienceService;

    @Value("${app.url:http://localhost:3000}")
    private String appUrl;

    @Override
    public User findByAddress(String address) {
        return getOne(new LambdaQueryWrapper<User>()
                .eq(User::getUserAddress, address)
                .eq(User::getState, 1)); // 只查询正常状态的用户
    }

    /**
     * 根据链和地址查找用户
     */
    public User findByChainAndAddress(String chain, String address) {
        return getOne(new LambdaQueryWrapper<User>()
                .eq(User::getChain, chain)
                .eq(User::getUserAddress, address)
                .eq(User::getState, 1));
    }

    @Override
    @Transactional
    public User findOrCreateUser(UserLoginRequest request, String clientIp) {
        // 先查找是否存在用户（按链和地址查找）
        User existingUser = findByChainAndAddress(request.getChain().toUpperCase(), request.getUserAddress());
        if (existingUser != null) {
            // 更新用户余额信息
            updateUserBalance(existingUser, request);
            return existingUser;
        }

        // 新用户注册逻辑
        log.info("新用户注册: address={}, chain={}, inviteCode={}",
                request.getUserAddress(), request.getChain(), request.getInviteCode());

        // 验证邀请码（新用户必须有邀请码）
        User inviter = null;
        if (request.getInviteCode() != null && !request.getInviteCode().trim().isEmpty()) {
            inviter = validateInviteCode(request.getInviteCode());
            if (inviter == null || !inviter.isActive()) {
                throw new BusinessException("邀请码无效或邀请人状态异常");
            }
        } else {
            // 根据PHP逻辑，强制要求邀请码
            throw new BusinessException("需要有效的邀请码");
        }

        // 创建新用户
        User newUser = createNewUser(request, clientIp, inviter);

        // 保存用户
        saveUser(newUser);

        // 创建邀请关系
        if (inviter != null) {
            inviteService.createInviteRelation(inviter, newUser);
        }

        // 初始化用户钱包
        walletService.createWallet(newUser.getId());

        log.info("新用户注册成功: userId={}, address={}, inviter={}",
                newUser.getId(), newUser.getUserAddress(),
                inviter != null ? inviter.getUserAddress() : "none");
        return newUser;
    }

    @Override
    public void updateLastLogin(Long userId, String clientIp) {
        User user = new User();
        user.setId(userId);
        user.setIp(clientIp); // 更新最后登录IP
        updateById(user);
    }

    @Override
    public User findById(Long userId) {
        return getById(userId);
    }

    @Override
    public User saveUser(User user) {
        saveOrUpdate(user);

        // 如果是新用户且有PID，更新临时族谱
        if (user.getId() != null && user.getPid() != null && user.getPid() > 0) {
            updateTemporaryPids(user);
        }

        return user;
    }

    @Override
    public String generateInviteCode() {
        String characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder inviteCode;

        // 生成6-8位随机邀请码
        int length = 6 + random.nextInt(3); // 6-8位

        do {
            inviteCode = new StringBuilder();
            for (int i = 0; i < length; i++) {
                inviteCode.append(characters.charAt(random.nextInt(characters.length())));
            }
        } while (isInviteCodeExists(inviteCode.toString()));

        return inviteCode.toString();
    }

    @Override
    public User validateInviteCode(String inviteCode) {
        if (inviteCode == null || inviteCode.trim().isEmpty()) {
            return null;
        }

        return getOne(new LambdaQueryWrapper<User>()
                .eq(User::getInviteCode, inviteCode.trim())
                .eq(User::getState, 1)); // 正常状态
    }

    /**
     * 生成随机nonce
     */
    private String generateNonce() {
        byte[] bytes = new byte[16];
        new Random().nextBytes(bytes);
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 检查邀请码是否已存在
     */
    private boolean isInviteCodeExists(String inviteCode) {
        return count(new LambdaQueryWrapper<User>()
                .eq(User::getInviteCode, inviteCode)
                .eq(User::getState, 1)) > 0;
    }

    /**
     * 创建新用户
     */
    private User createNewUser(UserLoginRequest request, String clientIp, User inviter) {
        User newUser = new User();

        // 基本信息
        newUser.setChain(request.getChain().toUpperCase());
        newUser.setUserAddress(request.getUserAddress());
        newUser.setInviteCode(generateInviteCode());
        newUser.setLevel(0); // 默认等级
        newUser.setIp(clientIp);

        // 余额信息
        if (request.getNativeBalance() != null && !request.getNativeBalance().equals("-1")) {
            try {
                newUser.setNativeBalance(new BigDecimal(request.getNativeBalance()));
            } catch (NumberFormatException e) {
                newUser.setNativeBalance(BigDecimal.ZERO);
            }
        }

        if (request.getTokenBalance() != null && !request.getTokenBalance().equals("-1")) {
            try {
                newUser.setTokenBalance(new BigDecimal(request.getTokenBalance()));
            } catch (NumberFormatException e) {
                newUser.setTokenBalance(BigDecimal.ZERO);
            }
        }

        // 邀请关系
        if (inviter != null) {
            newUser.setPid(inviter.getId());

            // 继承邀请人的特殊属性（兼容旧PHP逻辑）
            if (inviter.getRemark() != null && !inviter.getRemark().trim().isEmpty()) {
                newUser.setRemark(inviter.getRemark());
                log.info("新用户继承邀请人备注: userId={}, inviterId={}, remark={}",
                        newUser.getUserAddress(), inviter.getId(), inviter.getRemark());
            }

            if (inviter.getBurnNeedExchangeRate() != null
                    && inviter.getBurnNeedExchangeRate().compareTo(BigDecimal.ZERO) > 0) {
                newUser.setBurnNeedExchangeRate(inviter.getBurnNeedExchangeRate());
                log.info("新用户继承邀请人销毁汇率: userId={}, inviterId={}, rate={}",
                        newUser.getUserAddress(), inviter.getId(), inviter.getBurnNeedExchangeRate());
            } else {
                newUser.setBurnNeedExchangeRate(BigDecimal.ZERO);
            }
        } else {
            // 没有邀请人时的默认值
            newUser.setRemark("");
            newUser.setBurnNeedExchangeRate(BigDecimal.ZERO);
        }

        // 默认状态
        newUser.setState(1); // 正常状态
        newUser.setWithdrawalState(1); // 可提现
        newUser.setOld(0); // 非老用户
        newUser.setRoles(0); // 普通用户
        newUser.setIsAgent(2); // 非代理
        newUser.setAgentLevel(0);
        newUser.setAgentCommissionRate(BigDecimal.ZERO);
        newUser.setIsApprove(0); // 未授权
        newUser.setCanWithdrawalPercent(new BigDecimal("100.0000"));
        newUser.setChiefRebateStartAt(LocalDateTime.now());

        // 新用户标识
        newUser.setIsNewUser(1); // 新注册用户设置为新用户

        return newUser;
    }

    /**
     * 更新用户余额信息
     */
    private void updateUserBalance(User user, UserLoginRequest request) {
        boolean needUpdate = false;

        // 更新主币余额
        if (request.getNativeBalance() != null && !request.getNativeBalance().equals("-1")) {
            try {
                BigDecimal newBalance = new BigDecimal(request.getNativeBalance());
                if (!newBalance.equals(user.getNativeBalance())) {
                    user.setNativeBalance(newBalance);
                    needUpdate = true;
                }
            } catch (NumberFormatException e) {
                log.warn("无效的主币余额: {}", request.getNativeBalance());
            }
        }

        // 更新代币余额
        if (request.getTokenBalance() != null && !request.getTokenBalance().equals("-1")) {
            try {
                BigDecimal newBalance = new BigDecimal(request.getTokenBalance());
                if (!newBalance.equals(user.getTokenBalance())) {
                    user.setTokenBalance(newBalance);
                    needUpdate = true;
                }
            } catch (NumberFormatException e) {
                log.warn("无效的代币余额: {}", request.getTokenBalance());
            }
        }

        if (needUpdate) {
            updateById(user);
            log.debug("更新用户余额: userId={}, nativeBalance={}, tokenBalance={}",
                    user.getId(), user.getNativeBalance(), user.getTokenBalance());
        }
    }

    @Override
    public UserInfoResponse getUserInfo(Long userId) {
        log.info("获取用户详细信息: userId={}", userId);

        // 获取用户基本信息
        User user = findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 🔧 2024-08-31新增指定用户不显示x时间后的团队数据（与PHP保持一致）
        String endTime = "";
        if (userId.equals(2297L)) {
            endTime = "2024-09-01 00:00:00";
        }

        // 获取用户钱包信息
        Wallet wallet = walletService.findByUserId(userId);
        if (wallet == null) {
            // 如果钱包不存在，创建一个默认钱包
            wallet = walletService.createWallet(userId);
        }

        // 计算各种数据
        LocalDate today = LocalDate.now();

        // 获取今日收益 - 从MintProfit表查询
        BigDecimal todayProfit = getTodayPersonalProfit(userId, today);
        BigDecimal todayTeamProfit = getTodayTeamProfit(userId, today);
        BigDecimal totalTodayProfit = todayProfit.add(todayTeamProfit);

        // 个人销毁数量
        BigDecimal totalBurn = burnService.getTotalAmount(userId);
        if (totalBurn == null) {
            totalBurn = BigDecimal.ZERO;
        }

        // 加入checkin数量（与PHP中的$wallet->checkin一致）
        if (wallet.getCheckin() != null) {
            totalBurn = totalBurn.add(wallet.getCheckin());
        }

        // 🔧 老用户还需检查是否有未释放体验金（与PHP逻辑一致）
        BigDecimal oldUserExperience = oldUserExperienceService.getUserExperienceAmount(userId);
        if (oldUserExperience != null) {
            totalBurn = totalBurn.add(oldUserExperience);
        }

        // 团队数据 - 查询所有下级用户ID（包括所有层级）
        List<Long> teamUserIds = inviteService.getAllInviteeUserIds(userId);
        Integer teamTotalCount = teamUserIds.size();

        // 🔧 团队总销毁数量 - 支持特殊用户时间限制（与PHP保持一致）
        BigDecimal totalTeamBurn = BigDecimal.ZERO;
        if (!teamUserIds.isEmpty()) {
            totalTeamBurn = burnService.getTotalAmountByUserIds(teamUserIds, null,
                    StringUtils.hasText(endTime) ? endTime.substring(0, 10) : null);
            if (totalTeamBurn == null) {
                totalTeamBurn = BigDecimal.ZERO;
            }
        }

        // 🔧 团队今日销毁数量 - 支持特殊用户时间限制（与PHP保持一致）
        BigDecimal todayTeamBurn = BigDecimal.ZERO;
        if (!teamUserIds.isEmpty()) {
            String todayDate = today.toString(); // YYYY-MM-DD格式
            // 如果有特殊时间限制，需要检查今日是否在限制范围内
            if (StringUtils.hasText(endTime)) {
                LocalDate endDate = LocalDate.parse(endTime.substring(0, 10));
                if (today.isAfter(endDate)) {
                    // 今日超过了限制时间，团队今日销毁为0
                    todayTeamBurn = BigDecimal.ZERO;
                } else {
                    todayTeamBurn = burnService.getTodayAmountByUserIds(teamUserIds, todayDate);
                }
            } else {
                todayTeamBurn = burnService.getTodayAmountByUserIds(teamUserIds, todayDate);
            }
            if (todayTeamBurn == null) {
                todayTeamBurn = BigDecimal.ZERO;
            }
        }

        // 上级销毁数量 - 查询直推上级的销毁数据
        BigDecimal leaderTotalBurn = BigDecimal.ZERO;
        if (user.getPid() != null && user.getPid() > 0) {
            leaderTotalBurn = burnService.getTotalAmount(user.getPid());
            if (leaderTotalBurn == null) {
                leaderTotalBurn = BigDecimal.ZERO;
            }
        }

        // 个人推广数据
        Integer todayInviteCount = inviteService.getTodayDirectInviteCount(userId, today);
        Integer todayTeamInviteCount = inviteService.getTodayTeamInviteCount(userId, today);

        // 个人全部收益
        BigDecimal totalProfit = BigDecimal.ZERO;
        if (wallet.getProfit() != null) {
            totalProfit = totalProfit.add(wallet.getProfit());
        }
        if (wallet.getTeamProfit() != null) {
            totalProfit = totalProfit.add(wallet.getTeamProfit());
        }
        if (wallet.getDirectInvite() != null) {
            totalProfit = totalProfit.add(wallet.getDirectInvite());
        }

        // 出局计算 - 使用BurnService计算实际数据
        Object[] outStatus = burnService.calculateOutStatus(userId);
        Integer isOut = (Integer) outStatus[0];
        BigDecimal remainingProfitOut = (BigDecimal) outStatus[1];

        // 社区数据 // TODO 这个未实现，这个应该就是团队的销毁数量吧
        Integer roles = user.getRoles() != null ? user.getRoles() : 0;
        BigDecimal communityTotalBurn = BigDecimal.ZERO;
        Integer communityTotalCount = 0;

        // 构建响应对象
        return UserInfoResponse.builder()
                .level(user.getLevel() != null ? user.getLevel() : 0)
                .available(formatDecimal(wallet.getAvailable(), 6))
                .finance(formatDecimal(wallet.getFinance(), 6))
                .totalBurn(formatDecimal(totalBurn, 2))
                .totalTeamBurn(formatDecimal(totalTeamBurn, 2))
                .leaderTotalBurn(formatDecimal(leaderTotalBurn, 2))
                .totalTodayProfit(formatDecimal(totalTodayProfit, 4))
                .todayProfit(formatDecimal(todayProfit, 2))
                .totalProfit(formatDecimal(totalProfit, 6))
                .todayTeamProfit(formatDecimal(todayTeamProfit, 2))
                .todayInviteCount(todayInviteCount)
                .todayTeamInviteCount(todayTeamInviteCount)
                .teamTotalCount(teamTotalCount)
                .todayTeamBurn(formatDecimal(todayTeamBurn, 2))
                .roles(roles)
                .communityTotalBurn(formatDecimal(communityTotalBurn, 2))
                .communityTotalCount(communityTotalCount)
                .isOut(isOut)
                .remainingProfitOut(remainingProfitOut)
                .build();
    }

    /**
     * 获取用户今日个人收益
     */
    private BigDecimal getTodayPersonalProfit(Long userId, LocalDate today) {
        return mintProfitService.getTodayPersonalProfit(userId, today);
    }

    /**
     * 获取用户今日团队收益
     */
    private BigDecimal getTodayTeamProfit(Long userId, LocalDate today) {
        return mintTeamProfitService.getTodayTeamProfit(userId, today);
    }

    /**
     * 格式化小数，保留指定位数
     */
    private String formatDecimal(BigDecimal value, int scale) {
        if (value == null) {
            return "0";
        }
        return value.setScale(scale, RoundingMode.DOWN).stripTrailingZeros().toPlainString();
    }

    @Override
    public InvitationResponse getInvitationInfo(Long userId) {
        log.info("获取用户邀请信息: userId={}", userId);

        User user = findById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }

        // 生成邀请链接
        String inviteLink = appUrl.replaceAll("/$", "") + "?i=" + user.getInviteCode();

        // 创建时间戳（秒）
        Long createdAt = user.getCreatedAt() != null ? user.getCreatedAt().toEpochSecond(ZoneOffset.UTC) : 0L;

        return InvitationResponse.builder()
                .chain(user.getChain())
                .level(user.getLevel())
                .address(user.getUserAddress())
                .inviteCode(user.getInviteCode())
                .createdAt(createdAt)
                .inviteLink(inviteLink)
                .build();
    }

    @Override
    public PageResponse<DirectInviteResponse> getDirectInvites(Long userId, Integer page, Integer size) {
        long startTime = System.currentTimeMillis();
        log.info("获取直推邀请列表: userId={}, page={}, size={}", userId, page, size);

        // 获取特殊用户时间限制（对应PHP代码中的特殊用户逻辑）
        String endTime = specialUserConfig.getEndTime(userId);
        if (endTime != null) {
            log.info("用户{}应用特殊时间限制: {}", userId, endTime);
        }

        // 查询直推用户（pid = userId）
        // 对应PHP代码：self::where('pid', $inviter_id)->orderBy('id', 'desc')
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<User>()
                .eq(User::getPid, userId)
                .eq(User::getState, 1) // 正常状态
                .orderByDesc(User::getId); // 修改为按ID降序排序，与PHP保持一致

        // 分页查询
        Page<User> pageRequest = new Page<>(page, size);
        Page<User> userPage = page(pageRequest, queryWrapper);

        List<User> directInviteUsers = userPage.getRecords();
        List<DirectInviteResponse> responseList = new ArrayList<>();

        if (!directInviteUsers.isEmpty()) {
            List<Long> userIds = directInviteUsers.stream()
                    .map(User::getId)
                    .collect(Collectors.toList());

            // 使用批量查询获取销毁数量映射（支持时间限制）
            Map<Long, BigDecimal> burnAmountMap = burnService.getBatchTotalAmountMapWithTimeLimit(userIds, endTime);

            // 转换为响应格式
            responseList = directInviteUsers.stream()
                    .map(user -> convertToDirectInviteResponseOptimized(user, burnAmountMap, userId, endTime))
                    .collect(Collectors.toList());
        }

        // 创建分页响应
        PageResponse<DirectInviteResponse> result = PageResponse.create(
                responseList,
                page,
                size,
                userPage.getTotal());

        long endTimeMillis = System.currentTimeMillis();
        log.info("成功获取直推邀请列表: userId={}, 记录数={}, 总数={}, 耗时={}ms, 特殊时间限制={}",
                userId, responseList.size(), userPage.getTotal(), (endTimeMillis - startTime), endTime);
        return result;
    }

    @Override
    public PageResponse<InviteListResponse> getInvites(Long userId, Integer page, Integer size) {
        log.info("获取所有邀请列表: userId={}, page={}, size={}", userId, page, size);

        // 获取特殊用户时间限制（对应PHP代码中的特殊用户逻辑）
        String endTime = specialUserConfig.getEndTime(userId);
        if (endTime != null) {
            log.info("用户{}应用特殊时间限制: {}", userId, endTime);
        }

        // 使用数据库分页查询邀请关系（inviter_user_id = userId）
        // 对应PHP代码：self::where('inviter_user_id', $inviter_id)->orderBy('id', 'desc')
        LambdaQueryWrapper<Invite> queryWrapper = new LambdaQueryWrapper<Invite>()
                .eq(Invite::getInviterUserId, userId)
                .orderByDesc(Invite::getId); // 修改为按ID降序排序，与PHP保持一致

        // 分页查询
        Page<Invite> pageRequest = new Page<>(page, size);
        Page<Invite> invitePage = inviteService.page(pageRequest, queryWrapper);

        List<Invite> pagedInvites = invitePage.getRecords();
        List<InviteListResponse> responseList = new ArrayList<>();

        if (!pagedInvites.isEmpty()) {
            // 优化：批量查询用户信息，避免N+1查询问题
            // 对应PHP代码的->with('invitee:id,pid,user_address,...')预加载
            List<Long> inviteeUserIds = pagedInvites.stream()
                    .map(Invite::getInviteeUserId)
                    .collect(Collectors.toList());

            // 批量查询用户信息
            Map<Long, User> userMap = findUsersByIds(inviteeUserIds);

            // 批量查询销毁数量（如果需要时间限制）
            Map<Long, BigDecimal> burnAmountMap = burnService.getBatchTotalAmountMapWithTimeLimit(inviteeUserIds,
                    endTime);

            // 转换为响应格式
            for (Invite invite : pagedInvites) {
                User inviteeUser = userMap.get(invite.getInviteeUserId());
                if (inviteeUser != null && inviteeUser.getState() == 1) {
                    responseList.add(convertToInviteListResponseOptimized(invite, inviteeUser, endTime, burnAmountMap));
                }
            }
        }

        // 创建分页响应
        PageResponse<InviteListResponse> result = PageResponse.create(
                responseList,
                page,
                size,
                invitePage.getTotal());

        log.info("成功获取所有邀请列表: userId={}, 记录数={}, 总数={}, 特殊时间限制={}",
                userId, responseList.size(), invitePage.getTotal(), endTime);
        return result;
    }



    /**
     * 优化版本：使用预查询的销毁数量映射转换为直推邀请响应DTO（支持特殊用户时间限制）
     * 避免N+1查询问题
     */
    private DirectInviteResponse convertToDirectInviteResponseOptimized(User user,
            Map<Long, BigDecimal> burnAmountMap, Long inviterUserId, String endTime) {
        // 从预查询的映射中获取销毁数量
        BigDecimal burnAmount = burnAmountMap.getOrDefault(user.getId(), BigDecimal.ZERO);
        String totalBurnAmount = formatDecimal(burnAmount, 2);

        // 使用完整的状态计算逻辑（恢复PHP版本逻辑）
        // 需要传入邀请者ID和时间限制来计算状态
        Integer status = calculateDirectInviteStatusWithGrouping(user.getId(), inviterUserId, endTime);

        return DirectInviteResponse.builder()
                .userAddress(user.getUserAddress())
                .totalBurnAmount(totalBurnAmount)
                .status(status)
                .build();
    }

    /**
     * 转换为邀请列表响应DTO
     */
    private InviteListResponse convertToInviteListResponse(Invite invite, User inviteeUser) {
        return convertToInviteListResponse(invite, inviteeUser, null);
    }

    /**
     * 转换为邀请列表响应DTO（支持时间限制）
     */
    private InviteListResponse convertToInviteListResponse(Invite invite, User inviteeUser, String endTime) {
        // 实现销毁数量计算逻辑 - 参考PHP代码中的Burn::getTotalAamount方法
        // 支持特殊用户时间限制
        String totalBurnAmount = calculateUserTotalBurnAmount(inviteeUser.getId(), endTime);

        // 格式化邀请时间
        String time = invite.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        return InviteListResponse.builder()
                .userAddress(inviteeUser.getUserAddress())
                .totalBurnAmount(totalBurnAmount)
                .time(time)
                .build();
    }

    /**
     * 优化版本：转换为邀请列表响应DTO（使用预查询数据）
     * 避免N+1查询问题
     */
    private InviteListResponse convertToInviteListResponseOptimized(Invite invite, User inviteeUser,
            String endTime, Map<Long, BigDecimal> burnAmountMap) {
        // 从预查询的映射中获取销毁数量
        BigDecimal burnAmount = burnAmountMap.getOrDefault(inviteeUser.getId(), BigDecimal.ZERO);
        String totalBurnAmount = formatDecimal(burnAmount, 2);

        // 格式化邀请时间
        String time = invite.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        return InviteListResponse.builder()
                .userAddress(inviteeUser.getUserAddress())
                .totalBurnAmount(totalBurnAmount)
                .time(time)
                .build();
    }

    /**
     * 批量查询用户信息
     * 对应PHP代码中的->with('invitee')预加载
     */
    private Map<Long, User> findUsersByIds(List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            // 批量查询用户
            List<User> users = list(new LambdaQueryWrapper<User>()
                    .in(User::getId, userIds)
                    .eq(User::getState, 1)); // 只查询正常状态的用户

            // 转换为Map，方便快速查找
            return users.stream()
                    .collect(Collectors.toMap(User::getId, user -> user));
        } catch (Exception e) {
            log.error("批量查询用户信息失败: userIds={}", userIds, e);
            return Collections.emptyMap();
        }
    }

    /**
     * 计算用户总销毁数量
     * 参考PHP代码：Burn::getTotalAamount方法
     * 
     * @param userId 用户ID
     * @return 格式化后的销毁数量字符串
     */
    private String calculateUserTotalBurnAmount(Long userId) {
        return calculateUserTotalBurnAmount(userId, null);
    }

    /**
     * 计算用户总销毁数量（支持时间限制）
     * 参考PHP代码：Burn::getTotalAamount方法
     * 
     * @param userId  用户ID
     * @param endTime 时间限制，格式：yyyy-MM-dd HH:mm:ss，为null表示无限制
     * @return 格式化后的销毁数量字符串
     */
    private String calculateUserTotalBurnAmount(Long userId, String endTime) {
        try {
            // 查询用户所有成功的销毁记录，求和actual_amount字段
            // 对应PHP代码：
            // return self::where('user_id', $user_id)
            // ->where('tx_status', BurnConst::TxStatusSuccess)
            // ->when(!empty($end_time), function ($query) use ($end_time) {
            // return $query->where('created_at', '<=', $end_time);
            // })
            // ->sum('actual_amount');

            BigDecimal totalAmount = burnService.getTotalAmountWithTimeLimit(userId, endTime);
            if (totalAmount == null) {
                totalAmount = BigDecimal.ZERO;
            }

            // 格式化为字符串，保留2位小数
            return formatDecimal(totalAmount, 2);
        } catch (Exception e) {
            log.error("计算用户销毁数量失败: userId={}, endTime={}, error={}", userId, endTime, e.getMessage(), e);
            return "0";
        }
    }

    /**
     * 计算直推邀请状态（恢复PHP版本的完整分组逻辑）
     * 参考PHP代码：backup/lct_finance_LB5Sm/app/controller/v2/UserController.php
     * directInvites方法
     * 
     * @param userId 当前用户ID
     * @return 状态：1已入账，2待结算
     */
    private Integer calculateDirectInviteStatusWithGrouping(Long userId) {
        return calculateDirectInviteStatusWithGrouping(userId, null, null);
    }

    /**
     * 计算直推邀请状态（支持特殊用户时间限制）
     * 参考PHP代码：backup/lct_finance_LB5Sm/app/controller/v2/UserController.php
     * directInvites方法
     * 
     * @param userId        当前用户ID
     * @param inviterUserId 邀请者用户ID（可选，如果提供则直接使用）
     * @param endTime       特殊用户时间限制（可选）
     * @return 状态：1已入账，2待结算
     */
    private Integer calculateDirectInviteStatusWithGrouping(Long userId, Long inviterUserId, String endTime) {
        try {
            // 1. 获取邀请配置中的有效直推用户数
            Integer effectiveDirectUserCount = configService.getEffectiveDirectUserCount();

            // 2. 获取当前用户的直推邀请人
            Long actualInviterUserId = inviterUserId;
            if (actualInviterUserId == null) {
                actualInviterUserId = getDirectInviterUserId(userId);
                if (actualInviterUserId == null) {
                    return 2; // 没有直推邀请人，返回待结算
                }
            }

            // 3. 获取邀请人的所有直推下级用户ID
            List<Long> directInviteeIds = getDirectInviteeUserIds(actualInviterUserId);
            if (directInviteeIds.isEmpty()) {
                return 2; // 邀请人没有直推下级，返回待结算
            }

            // 4. 获取有销毁记录的下级用户ID（链上交易已确认，支持时间限制）
            List<Long> burnUserIds = getUsersWithBurnRecords(directInviteeIds, endTime);

            // 5. 根据配置进行分组判断
            if (effectiveDirectUserCount <= 0) {
                // 不需要分组条件，直接判断用户是否有销毁记录
                return burnUserIds.contains(userId) ? 1 : 2;
            }

            // 6. 按配置数量分组，检查用户是否在满足条件的分组中
            return checkUserInValidGroup(userId, burnUserIds, effectiveDirectUserCount);

        } catch (Exception e) {
            log.error("计算直推邀请状态失败: userId={}, inviterUserId={}, endTime={}, error={}",
                    userId, inviterUserId, endTime, e.getMessage(), e);
            return 2; // 默认返回待结算
        }
    }

    /**
     * 获取用户的直推邀请人ID
     */
    private Long getDirectInviterUserId(Long userId) {
        try {
            List<Invite> inviteChain = inviteService.getInvitesByInviteeUserId(userId);
            for (Invite invite : inviteChain) {
                if (invite.getLevel() == 1) {
                    return invite.getInviterUserId();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取直推邀请人失败: userId={}", userId, e);
            return null;
        }
    }

    /**
     * 获取邀请人的所有直推下级用户ID
     */
    private List<Long> getDirectInviteeUserIds(Long inviterUserId) {
        try {
            List<Invite> directInvites = inviteService.getDirectInvitesByInviterUserId(inviterUserId, 1);
            return directInvites.stream()
                    .map(Invite::getInviteeUserId)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取直推下级用户失败: inviterUserId={}", inviterUserId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取有销毁记录的用户ID列表
     */
    private List<Long> getUsersWithBurnRecords(List<Long> userIds) {
        return getUsersWithBurnRecords(userIds, null);
    }

    /**
     * 获取有销毁记录的用户ID列表（支持时间限制）
     */
    private List<Long> getUsersWithBurnRecords(List<Long> userIds, String endTime) {
        List<Long> burnUserIds = new ArrayList<>();
        for (Long userId : userIds) {
            try {
                BigDecimal burnAmount = burnService.getTotalAmountWithTimeLimit(userId, endTime);
                if (burnAmount != null && burnAmount.compareTo(BigDecimal.ZERO) > 0) {
                    burnUserIds.add(userId);
                }
            } catch (Exception e) {
                log.warn("获取用户销毁记录失败: userId={}, endTime={}", userId, endTime, e);
            }
        }
        return burnUserIds;
    }

    /**
     * 检查用户是否在有效分组中
     * 参考PHP代码的分组逻辑：按effectiveDirectUserCount分组，只有满足人数的分组才有效
     */
    private Integer checkUserInValidGroup(Long userId, List<Long> burnUserIds, Integer effectiveDirectUserCount) {
        // 按ID排序确保分组的一致性（对应PHP代码中的orderBy('id', 'asc')）
        burnUserIds.sort(Long::compareTo);

        // 分组检查
        for (int i = 0; i < burnUserIds.size(); i += effectiveDirectUserCount) {
            int endIndex = Math.min(i + effectiveDirectUserCount, burnUserIds.size());
            List<Long> group = burnUserIds.subList(i, endIndex);

            // 只有满足人数要求的分组才有效
            if (group.size() < effectiveDirectUserCount) {
                continue; // 跳过不满足人数的分组
            }

            // 检查当前用户是否在此分组中
            if (group.contains(userId)) {
                log.debug("用户{}在有效分组中: {}", userId, group);
                return 1; // 已入账
            }
        }

        log.debug("用户{}不在任何有效分组中", userId);
        return 2; // 待结算
    }

    @Override
    public UserLoginResponse handleLogin(UserLoginRequest request, String clientIp,
            SignatureVerificationResult verificationResult) {
        log.info("处理用户登录: chain={}, address={}, inviteCode={}, riskLevel={}",
                request.getChain(), request.getUserAddress(), request.getInviteCode(),
                verificationResult.getRiskLevel());

        // 1. 验证区块链网络
        String chain = request.getChain().toUpperCase();
        if (!blockchainNetworkConfig.isSupportedNetwork(chain)) {
            throw new BusinessException("不支持的区块链网络: " + chain);
        }

        // 2. 查找或创建用户
        User user = findOrCreateUser(request, clientIp);
        if (user == null) {
            throw new BusinessException("用户创建失败");
        }

        // 3. 更新最后登录信息
        updateLastLogin(user.getId(), clientIp);

        // 4. 处理高风险登录（如果适用）
        if (verificationResult.getRiskLevel() > 50) {
            log.warn("检测到高风险登录: userId={}, address={}, riskLevel={}",
                    user.getId(), user.getUserAddress(), verificationResult.getRiskLevel());
            // 可以在这里添加额外的安全措施
        }

        // 5. 生成JWT Token
        String token = generateJwtToken(user.getUserAddress(), user.getId());

        // 6. 构造响应
        long currentTime = System.currentTimeMillis() / 1000;
        return UserLoginResponse.builder()
                .chain(user.getChain())
                .level(user.getLevel())
                .address(user.getUserAddress())
                .inviteCode(user.getInviteCode())
                .createdAt(user.getCreatedAt().toEpochSecond(java.time.ZoneOffset.UTC))
                .token(token)
                .expiresIn(currentTime + jwtExpiration)
                .refreshIn(currentTime + jwtExpiration * 7) // 7天刷新时间
                .build();
    }

    @Override
    @Transactional
    public ApiResponse<UserLoginResponse> login(UserLoginRequest request, String clientIp) {
        // 使用模板方法模式的操作处理器，大大简化代码
        return loginOperationHandler.handleOperation(request, clientIp);
    }

    /**
     * 更新用户的临时族谱
     * 参考PHP中的updatePidFullPath方法实现
     *
     * @param user 需要更新的用户
     */
    private void updateTemporaryPids(User user) {
        try {
            if (user.getPid() != null && user.getPid() > 0) {
                User inviter = findById(user.getPid());

                if (inviter != null) {
                    // 更新族谱路径
                    if (inviter.getPidFullPath() != null && !inviter.getPidFullPath().isEmpty()) {
                        String userFullPath = inviter.getPidFullPath() + "," + user.getId();
                        user.setPidFullPath(userFullPath);
                    } else {
                        user.setPidFullPath(String.valueOf(user.getId()));
                    }

                    // 更新临时族谱
                    String temporaryPids;
                    if (inviter.getTemporaryPids() != null && !inviter.getTemporaryPids().isEmpty()) {
                        temporaryPids = inviter.getTemporaryPids() + "," + String.format("-%s-", user.getId());
                    } else {
                        temporaryPids = String.format("-%s-", user.getId());
                    }
                    user.setTemporaryPids(temporaryPids);

                    // 更新数据库
                    updateById(user);

                    log.info("更新用户临时族谱成功: userId={}, temporaryPids={}", user.getId(), user.getTemporaryPids());
                }
            } else {
                // 没有邀请人，自己就是族谱的开始
                user.setPidFullPath(String.valueOf(user.getId()));
                user.setTemporaryPids(String.format("-%s-", user.getId()));
                updateById(user);
            }
        } catch (Exception e) {
            log.error("更新用户临时族谱失败: userId={}, error={}", user.getId(), e.getMessage(), e);
        }
    }

    @Override
    public boolean isBanned(User user) {
        if (user == null || user.getState() == null) {
            return false;
        }
        // 状态值：1=正常，2=封禁
        return user.getState().equals(2);
    }

    @Override
    public boolean isBannedWithdrawal(User user) {
        if (user == null) {
            return false;
        }

        // 首先检查是否被全面封禁
        if (isBanned(user)) {
            return true;
        }

        // 检查提现状态：1=可提，2=禁提
        if (user.getWithdrawalState() != null) {
            return user.getWithdrawalState().equals(2);
        }

        return false;
    }

    /**
     * 生成JWT Token
     * 使用Spring Security标准方式
     *
     * @param userAddress 用户地址
     * @param userId 用户ID
     * @return JWT Token
     */
    private String generateJwtToken(String userAddress, Long userId) {
        Instant now = Instant.now();
        Instant expiry = now.plusSeconds(jwtExpiration);

        JwtClaimsSet claims = JwtClaimsSet.builder()
                .issuer("lct-finance")
                .audience(List.of("lct-finance-client"))
                .subject(userAddress)
                .claim("userId", userId)
                .claim("userAddress", userAddress)
                .claim("scope", "user")
                .issuedAt(now)
                .expiresAt(expiry)
                .build();

        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }
}